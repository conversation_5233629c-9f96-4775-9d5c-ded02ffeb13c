/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #8B4513;
    --secondary-color: #A0522D;
    --accent-color: #D2B48C;
    --light-bg: #F5F5DC;
    --card-bg: #FAEBD7;
    --dark-brown: #654321;
    --text-primary: #2F1B14;
    --text-secondary: #5D4037;
    --text-light: #8D6E63;
    --gradient-primary: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
    --gradient-secondary: linear-gradient(135deg, #D2B48C 0%, #F5DEB3 100%);
    --shadow-elegant: 0 8px 32px rgba(139, 69, 19, 0.15);
    --shadow-card: 0 4px 16px rgba(139, 69, 19, 0.1);
    --border-color: rgba(139, 69, 19, 0.2);
}

body {
    font-family: 'Exo 2', sans-serif;
    background: var(--light-bg);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Animated Background */
.bg-animation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: linear-gradient(45deg, #F5F5DC 0%, #FAEBD7 50%, #F5DEB3 100%);
}

.floating-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(1px 1px at 20px 30px, rgba(139, 69, 19, 0.1), transparent),
        radial-gradient(1px 1px at 40px 70px, rgba(160, 82, 45, 0.1), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(210, 180, 140, 0.2), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(139, 69, 19, 0.1), transparent);
    background-repeat: repeat;
    background-size: 150px 150px;
    animation: float 25s linear infinite;
}

.grid-overlay {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(139, 69, 19, 0.05) 1px, transparent 1px),
        linear-gradient(90deg, rgba(139, 69, 19, 0.05) 1px, transparent 1px);
    background-size: 60px 60px;
    opacity: 0.4;
}

@keyframes float {
    0% { transform: translateY(0px) translateX(0px); }
    33% { transform: translateY(-30px) translateX(20px); }
    66% { transform: translateY(20px) translateX(-20px); }
    100% { transform: translateY(0px) translateX(0px); }
}

/* Header Styles */
.header {
    background: rgba(245, 245, 220, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 2px solid var(--primary-color);
    padding: 25px 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: var(--shadow-card);
}

.header-content {
    display: flex;
    align-items: center;
    gap: 30px;
}

.logo-container {
    position: relative;
}

.logo {
    height: 70px;
    width: auto;
    border-radius: 12px;
    filter: drop-shadow(0 4px 8px rgba(139, 69, 19, 0.3));
    transition: all 0.3s ease;
}

.logo:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 6px 12px rgba(139, 69, 19, 0.4));
}

.logo-glow {
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: var(--gradient-primary);
    border-radius: 15px;
    opacity: 0;
    z-index: -1;
    transition: opacity 0.3s ease;
}

.logo-container:hover .logo-glow {
    opacity: 0.2;
}

.brand-title {
    font-family: 'Orbitron', monospace;
    font-size: 2.8rem;
    font-weight: 700;
    margin: 0;
    color: var(--primary-color);
    text-shadow: 2px 2px 4px rgba(139, 69, 19, 0.3);
    letter-spacing: 1px;
}

.domain {
    color: var(--accent-color);
}

.tagline {
    color: var(--text-secondary);
    font-size: 1rem;
    margin-top: 8px;
    letter-spacing: 2px;
    text-transform: uppercase;
    font-weight: 300;
}

.text-glow {
    animation: subtle-glow 3s ease-in-out infinite alternate;
}

@keyframes subtle-glow {
    from { text-shadow: 2px 2px 4px rgba(139, 69, 19, 0.3); }
    to { text-shadow: 2px 2px 8px rgba(139, 69, 19, 0.5), 0 0 15px rgba(139, 69, 19, 0.2); }
}

/* Hero Section */
.hero {
    padding: 120px 0;
    background: linear-gradient(135deg, var(--card-bg) 0%, var(--light-bg) 100%);
    position: relative;
    overflow: hidden;
    border-bottom: 1px solid var(--border-color);
}

.hero .container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.hero-content {
    z-index: 2;
}

.hero-title {
    font-family: 'Orbitron', monospace;
    font-size: 3.2rem;
    font-weight: 700;
    margin-bottom: 30px;
    color: var(--primary-color);
    line-height: 1.3;
    text-shadow: 2px 2px 4px rgba(139, 69, 19, 0.2);
}

.typing-text {
    display: inline-block;
    border-right: 3px solid var(--primary-color);
    animation: typing 5s steps(50) infinite, blink 1.5s infinite;
}

@keyframes typing {
    0%, 40% { width: 0; }
    80%, 100% { width: 100%; }
}

@keyframes blink {
    0%, 50% { border-color: var(--primary-color); }
    51%, 100% { border-color: transparent; }
}

.hero-description {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 40px;
    line-height: 1.8;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    border: none;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-elegant);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 24px rgba(139, 69, 19, 0.25);
    background: linear-gradient(135deg, #A0522D 0%, #8B4513 100%);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-elegant);
}

/* Hologram Animation */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.hologram-container {
    position: relative;
    width: 300px;
    height: 300px;
}

.hologram-ring {
    position: absolute;
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    opacity: 0.6;
}

.hologram-ring:nth-child(1) {
    width: 100%;
    height: 100%;
    animation: rotate 10s linear infinite;
}

.hologram-ring:nth-child(2) {
    width: 70%;
    height: 70%;
    top: 15%;
    left: 15%;
    animation: rotate 8s linear infinite reverse;
}

.hologram-ring:nth-child(3) {
    width: 40%;
    height: 40%;
    top: 30%;
    left: 30%;
    animation: rotate 6s linear infinite;
}

.hologram-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 4rem;
    color: var(--primary-color);
    animation: pulse 2s ease-in-out infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
    50% { transform: translate(-50%, -50%) scale(1.1); opacity: 0.8; }
}

/* Services Section */
.services {
    padding: 100px 0;
    background: var(--darker-bg);
    position: relative;
}

.section-title {
    font-family: 'Orbitron', monospace;
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 30px;
    color: var(--text-primary);
}

.title-line {
    width: 100px;
    height: 2px;
    background: var(--gradient-primary);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
.service-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    padding: 40px 30px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-card);
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(139, 69, 19, 0.05), transparent);
    transition: left 0.6s ease;
}

.service-card:hover::before {
    left: 100%;
}

.service-card:hover {
    transform: translateY(-8px);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-elegant);
}

.service-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 25px;
    animation: gentle-float 4s ease-in-out infinite;
}

.service-card h4 {
    font-family: 'Orbitron', monospace;
    font-size: 1.4rem;
    color: var(--text-primary);
    margin-bottom: 15px;
    font-weight: 600;
}

.service-card p {
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: 25px;
    font-size: 1rem;
}

.service-features {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
}

.feature-tag {
    background: rgba(139, 69, 19, 0.1);
    color: var(--primary-color);
    padding: 6px 14px;
    border-radius: 20px;
    font-size: 0.85rem;
    border: 1px solid rgba(139, 69, 19, 0.2);
    transition: all 0.3s ease;
    font-weight: 500;
}

@keyframes gentle-float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Services Section */
.services {
    padding: 100px 0;
    background: var(--light-bg);
    position: relative;
    border-top: 1px solid var(--border-color);
}

.section-title {
    font-family: 'Orbitron', monospace;
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 30px;
    color: var(--text-primary);
    font-weight: 600;
}

.title-line {
    width: 80px;
    height: 3px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
    margin-top: 60px;
}

/* Location Section */
.location {
    padding: 100px 0;
    background: var(--card-bg);
    position: relative;
    border-top: 1px solid var(--border-color);
}

.location-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.location-info h3 {
    font-family: 'Orbitron', monospace;
    font-size: 2.5rem;
    color: var(--text-primary);
    margin-bottom: 25px;
    font-weight: 600;
}

.location-info p {
    color: var(--text-secondary);
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 35px;
}

.location-features {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 15px;
    color: var(--text-secondary);
    font-size: 1.1rem;
}

.feature-item i {
    color: var(--primary-color);
    font-size: 1.3rem;
    width: 25px;
}

.location-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.map-container {
    position: relative;
    width: 250px;
    height: 250px;
}

.pulse-dot {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    background: var(--accent-color);
    border-radius: 50%;
    animation: pulse-dot 2.5s ease-in-out infinite;
    box-shadow: 0 0 0 0 rgba(210, 180, 140, 0.7);
}

.location-rings {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.ring {
    position: absolute;
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    opacity: 0.4;
}

.ring:nth-child(1) {
    width: 80px;
    height: 80px;
    top: -40px;
    left: -40px;
    animation: ring-pulse 3s ease-in-out infinite;
}

.ring:nth-child(2) {
    width: 140px;
    height: 140px;
    top: -70px;
    left: -70px;
    animation: ring-pulse 3s ease-in-out infinite 1s;
}

.ring:nth-child(3) {
    width: 200px;
    height: 200px;
    top: -100px;
    left: -100px;
    animation: ring-pulse 3s ease-in-out infinite 2s;
}

@keyframes pulse-dot {
    0% {
        transform: translate(-50%, -50%) scale(1);
        box-shadow: 0 0 0 0 rgba(210, 180, 140, 0.7);
    }
    70% {
        transform: translate(-50%, -50%) scale(1.1);
        box-shadow: 0 0 0 20px rgba(210, 180, 140, 0);
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
        box-shadow: 0 0 0 0 rgba(210, 180, 140, 0);
    }
}

@keyframes ring-pulse {
    0% { transform: scale(0.8); opacity: 0.6; }
    50% { transform: scale(1); opacity: 0.3; }
    100% { transform: scale(1.2); opacity: 0; }
}
    margin-top: 60px;
}
/* Contact Section */
.contact {
    padding: 100px 0;
    background: var(--light-bg);
    border-top: 1px solid var(--border-color);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: start;
    margin-top: 40px;
}

.contact-info h4 {
    font-family: 'Orbitron', monospace;
    font-size: 1.8rem;
    color: var(--text-primary);
    margin-bottom: 20px;
    font-weight: 600;
}

.contact-info p {
    color: var(--text-secondary);
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 30px;
}

.contact-methods {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.contact-method {
    display: flex;
    align-items: center;
    gap: 15px;
    color: var(--text-secondary);
    font-size: 1.1rem;
    padding: 15px 0;
    border-bottom: 1px solid var(--border-color);
}

.contact-method i {
    color: var(--primary-color);
    font-size: 1.3rem;
    width: 25px;
}

.contact-form {
    background: var(--card-bg);
    padding: 40px;
    border-radius: 15px;
    box-shadow: var(--shadow-card);
    border: 1px solid var(--border-color);
}

.form-group {
    position: relative;
    margin-bottom: 25px;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 15px 0;
    border: none;
    border-bottom: 2px solid var(--border-color);
    background: transparent;
    color: var(--text-primary);
    font-size: 1rem;
    transition: all 0.3s ease;
    font-family: 'Exo 2', sans-serif;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-bottom-color: var(--primary-color);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: var(--text-light);
}

.input-line {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width 0.3s ease;
}

.form-group.focused .input-line,
.form-group input:focus + .input-line,
.form-group textarea:focus + .input-line,
.form-group select:focus + .input-line {
    width: 100%;
}

.form-group select {
    cursor: pointer;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* Footer */
.footer {
    background: var(--primary-color);
    color: white;
    padding: 40px 0;
    border-top: 3px solid var(--secondary-color);
}

.footer-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 20px;
}

.footer-brand {
    display: flex;
    align-items: center;
    gap: 15px;
}

.footer-logo {
    height: 50px;
    width: auto;
    border-radius: 8px;
    filter: brightness(1.1);
}

.footer-brand p {
    font-family: 'Orbitron', monospace;
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
}

.footer-text p {
    margin: 0;
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.95rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero .container {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .brand-title {
        font-size: 2rem;
    }

    .header-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .location-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .hero-buttons {
        justify-content: center;
    }

    .section-title {
        font-size: 2rem;
        flex-direction: column;
        gap: 15px;
    }

    .title-line {
        width: 60px;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero-title {
        font-size: 2rem;
    }

    .brand-title {
        font-size: 1.5rem;
    }

    .service-card {
        padding: 30px 20px;
    }

    .contact-form {
        padding: 30px 20px;
    }

    .btn {
        padding: 12px 24px;
        font-size: 0.9rem;
    }
}