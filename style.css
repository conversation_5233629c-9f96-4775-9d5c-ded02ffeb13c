body {
    margin: 0;
    font-family: 'Segoe UI', 'Arial', sans-serif;
    background: #f6f1eb;
    color: #2d1c10;
}
header {
    background: #ede3d1;
    border-bottom: 2px solid #a05a2c;
    padding: 32px 0 16px 0;
}
.container {
    width: 90%;
    max-width: 1100px;
    margin: 0 auto;
}
.header-flex {
    display: flex;
    align-items: center;
    gap: 32px;
}
.logo {
    height: 80px;
    width: auto;
    border-radius: 10px;
    background: #fff;
    box-shadow: 0 2px 8px rgba(160,90,44,0.08);
}
h1 {
    font-size: 2.8rem;
    margin: 0;
    font-family: 'Montserrat', 'Segoe UI', Arial, sans-serif;
    color: #2d1c10;
    letter-spacing: 2px;
}
.tagline {
    color: #a05a2c;
    font-size: 1.2rem;
    margin-top: 6px;
    letter-spacing: 1px;
}
#hero {
    text-align: center;
    padding: 60px 0 40px 0;
    background: #f6f1eb;
}
#hero h2 {
    font-size: 2.2rem;
    color: #a05a2c;
    margin-bottom: 16px;
}
#hero p {
    font-size: 1.1rem;
    margin-bottom: 28px;
}
.btn {
    background: #a05a2c;
    color: #fff;
    padding: 12px 32px;
    border: none;
    border-radius: 24px;
    font-size: 1rem;
    text-decoration: none;
    cursor: pointer;
    transition: background 0.2s;
    font-weight: 600;
}
.btn:hover {
    background: #7b3e1c;
}
#services {
    background: #ede3d1;
    padding: 48px 0 32px 0;
    text-align: center;
}
#services h3 {
    color: #a05a2c;
    font-size: 1.6rem;
    margin-bottom: 32px;
}
.service-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 32px;
    justify-content: center;
}
.service-item {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(160,90,44,0.07);
    padding: 28px 24px;
    max-width: 320px;
    min-width: 220px;
    flex: 1 1 220px;
}
.service-item h4 {
    color: #a05a2c;
    margin-bottom: 10px;
    font-size: 1.2rem;
}
#about {
    padding: 48px 0 32px 0;
    background: #f6f1eb;
    text-align: center;
}
#about h3 {
    color: #a05a2c;
    font-size: 1.5rem;
    margin-bottom: 18px;
}
#contact {
    background: #ede3d1;
    padding: 48px 0 32px 0;
    text-align: center;
}
#contact h3 {
    color: #a05a2c;
    font-size: 1.5rem;
    margin-bottom: 18px;
}
form {
    display: flex;
    flex-direction: column;
    gap: 16px;
    max-width: 400px;
    margin: 0 auto;
}
input, textarea {
    padding: 12px;
    border: 1px solid #a05a2c;
    border-radius: 8px;
    font-size: 1rem;
    background: #fff;
    color: #2d1c10;
}
input:focus, textarea:focus {
    outline: 2px solid #a05a2c;
}
button[type="submit"] {
    margin-top: 8px;
}
footer {
    background: #a05a2c;
    color: #fff;
    text-align: center;
    padding: 18px 0;
    font-size: 1rem;
    letter-spacing: 1px;
}
@media (max-width: 700px) {
    .header-flex, .service-grid {
        flex-direction: column;
        gap: 18px;
    }
    .logo {
        height: 60px;
    }
    h1 {
        font-size: 2rem;
    }
}