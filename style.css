/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #00d4ff;
    --secondary-color: #0099cc;
    --accent-color: #ff6b35;
    --dark-bg: #0a0a0f;
    --darker-bg: #050508;
    --card-bg: rgba(15, 15, 25, 0.8);
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --text-accent: #00d4ff;
    --gradient-primary: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
    --gradient-secondary: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    --shadow-glow: 0 0 20px rgba(0, 212, 255, 0.3);
    --shadow-card: 0 8px 32px rgba(0, 0, 0, 0.3);
}

body {
    font-family: 'Exo 2', sans-serif;
    background: var(--dark-bg);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Animated Background */
.bg-animation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: linear-gradient(45deg, #0a0a0f 0%, #1a1a2e 50%, #0a0a0f 100%);
}

.floating-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, rgba(0, 212, 255, 0.3), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255, 107, 53, 0.3), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(0, 212, 255, 0.5), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255, 107, 53, 0.5), transparent);
    background-repeat: repeat;
    background-size: 200px 200px;
    animation: float 20s linear infinite;
}

.grid-overlay {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    opacity: 0.3;
}

@keyframes float {
    0% { transform: translateY(0px) translateX(0px); }
    33% { transform: translateY(-30px) translateX(20px); }
    66% { transform: translateY(20px) translateX(-20px); }
    100% { transform: translateY(0px) translateX(0px); }
}

/* Header Styles */
.header {
    background: rgba(10, 10, 15, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 212, 255, 0.3);
    padding: 20px 0;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    align-items: center;
    gap: 30px;
}

.logo-container {
    position: relative;
}

.logo {
    height: 60px;
    width: auto;
    border-radius: 12px;
    filter: drop-shadow(0 0 10px rgba(0, 212, 255, 0.5));
    transition: all 0.3s ease;
}

.logo:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 0 20px rgba(0, 212, 255, 0.8));
}

.logo-glow {
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: var(--gradient-primary);
    border-radius: 15px;
    opacity: 0;
    z-index: -1;
    transition: opacity 0.3s ease;
}

.logo-container:hover .logo-glow {
    opacity: 0.3;
}

.brand-title {
    font-family: 'Orbitron', monospace;
    font-size: 2.5rem;
    font-weight: 900;
    margin: 0;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
}

.domain {
    color: var(--accent-color);
}

.tagline {
    color: var(--text-secondary);
    font-size: 1rem;
    margin-top: 5px;
    letter-spacing: 2px;
    text-transform: uppercase;
}

.text-glow {
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { text-shadow: 0 0 20px rgba(0, 212, 255, 0.5); }
    to { text-shadow: 0 0 30px rgba(0, 212, 255, 0.8), 0 0 40px rgba(0, 212, 255, 0.6); }
}

/* Hero Section */
.hero {
    padding: 100px 0;
    background: radial-gradient(ellipse at center, rgba(0, 212, 255, 0.1) 0%, transparent 70%);
    position: relative;
    overflow: hidden;
}

.hero .container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.hero-content {
    z-index: 2;
}

.hero-title {
    font-family: 'Orbitron', monospace;
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 30px;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
}

.typing-text {
    display: inline-block;
    border-right: 3px solid var(--primary-color);
    animation: typing 4s steps(40) infinite, blink 1s infinite;
}

@keyframes typing {
    0%, 50% { width: 0; }
    100% { width: 100%; }
}

@keyframes blink {
    0%, 50% { border-color: var(--primary-color); }
    51%, 100% { border-color: transparent; }
}

.hero-description {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 40px;
    line-height: 1.8;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    border: none;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-glow);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 0 30px rgba(0, 212, 255, 0.6);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: var(--dark-bg);
    transform: translateY(-3px);
}

/* Hologram Animation */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.hologram-container {
    position: relative;
    width: 300px;
    height: 300px;
}

.hologram-ring {
    position: absolute;
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    opacity: 0.6;
}

.hologram-ring:nth-child(1) {
    width: 100%;
    height: 100%;
    animation: rotate 10s linear infinite;
}

.hologram-ring:nth-child(2) {
    width: 70%;
    height: 70%;
    top: 15%;
    left: 15%;
    animation: rotate 8s linear infinite reverse;
}

.hologram-ring:nth-child(3) {
    width: 40%;
    height: 40%;
    top: 30%;
    left: 30%;
    animation: rotate 6s linear infinite;
}

.hologram-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 4rem;
    color: var(--primary-color);
    animation: pulse 2s ease-in-out infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
    50% { transform: translate(-50%, -50%) scale(1.1); opacity: 0.8; }
}

/* Services Section */
.services {
    padding: 100px 0;
    background: var(--darker-bg);
    position: relative;
}

.section-title {
    font-family: 'Orbitron', monospace;
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 30px;
    color: var(--text-primary);
}

.title-line {
    width: 100px;
    height: 2px;
    background: var(--gradient-primary);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
    margin-top: 60px;
}
.service-card {
    background: var(--card-bg);
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 20px;
    padding: 40px 30px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.service-card:hover::before {
    left: 100%;
}

.service-card:hover {
    transform: translateY(-10px);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-glow);
}

.service-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 20px;
    animation: float 3s ease-in-out infinite;
}

.service-card h4 {
    font-family: 'Orbitron', monospace;
    font-size: 1.5rem;
    color: var(--text-primary);
    margin-bottom: 15px;
}

.service-card p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 20px;
}

.service-features {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
}

.feature-tag {
    background: rgba(0, 212, 255, 0.1);
    color: var(--primary-color);
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    border: 1px solid rgba(0, 212, 255, 0.3);
}

/* Location Section */
.location {
    padding: 100px 0;
    background: var(--dark-bg);
    position: relative;
}

.location-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.location-info h3 {
    font-family: 'Orbitron', monospace;
    font-size: 2.5rem;
    color: var(--text-primary);
    margin-bottom: 20px;
}

.location-info p {
    color: var(--text-secondary);
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 30px;
}

.location-features {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 15px;
    color: var(--text-secondary);
}

.feature-item i {
    color: var(--primary-color);
    font-size: 1.2rem;
    width: 20px;
}

.location-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.map-container {
    position: relative;
    width: 200px;
    height: 200px;
}

.pulse-dot {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    background: var(--accent-color);
    border-radius: 50%;
    animation: pulse-dot 2s ease-in-out infinite;
}

.location-rings {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.ring {
    position: absolute;
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    opacity: 0.6;
}

.ring:nth-child(1) {
    width: 60px;
    height: 60px;
    top: -30px;
    left: -30px;
    animation: ring-pulse 2s ease-in-out infinite;
}

.ring:nth-child(2) {
    width: 120px;
    height: 120px;
    top: -60px;
    left: -60px;
    animation: ring-pulse 2s ease-in-out infinite 0.5s;
}

.ring:nth-child(3) {
    width: 180px;
    height: 180px;
    top: -90px;
    left: -90px;
    animation: ring-pulse 2s ease-in-out infinite 1s;
}

@keyframes pulse-dot {
    0%, 100% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(1.2); }
}

@keyframes ring-pulse {
    0% { transform: scale(0.8); opacity: 0.8; }
    50% { transform: scale(1); opacity: 0.4; }
    100% { transform: scale(1.2); opacity: 0; }
}